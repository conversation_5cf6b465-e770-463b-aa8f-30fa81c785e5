# 🤖 Python AI Backend Setup

## 🎯 Tính năng mới: AI Enhanced Processing

Python backend sử dụng OpenCV và PIL để xử lý ảnh chất lượng cao, tạo ra ASCII art đẹp như ví dụ bạn mong muốn.

### ✨ Cải tiến so với JavaScript:

- **🔬 Advanced Image Processing**: CLAHE, Gaussian blur, bilateral filter
- **🎨 Floyd-Steinberg Dithering**: Tạo ra gradient mượt mà hơn
- **🧠 Edge Enhancement**: Unsharp mask để làm nổi bật chi tiết
- **⚡ Optimized Braille Mapping**: Thu<PERSON>t toán chính xác hơn
- **🎯 Fixed 38 Characters**: <PERSON><PERSON><PERSON> bảo mỗi dòng đúng 38 ký tự

## 🚀 Cách cài đặt và chạy:

### Phương pháp 1: Tự động (Khuyến nghị)
```bash
python setup_python_server.py
```

### Phương pháp 2: Thủ công
```bash
# 1. Cài đặt dependencies
pip install -r requirements.txt

# 2. Chạy server
python ascii_server.py
```

## 📋 Requirements:

- **Python 3.8+**
- **pip** (Python package manager)
- **Internet connection** (để tải packages)

## 🔧 Dependencies được cài đặt:

- `Flask` - Web server
- `Flask-CORS` - Cross-origin requests
- `Pillow` - Image processing
- `opencv-python` - Computer vision
- `numpy` - Numerical computing
- `scipy` - Scientific computing
- `scikit-image` - Image processing algorithms

## 🌐 Cách sử dụng:

1. **Chạy Python server**: `python setup_python_server.py`
2. **Chạy frontend**: `python -m http.server 8000` (terminal khác)
3. **Mở browser**: `http://localhost:8000`
4. **Bật AI Processing**: Checkbox "🤖 AI Enhanced Processing" đã được tích sẵn
5. **Upload ảnh và tạo ASCII art**

## 🎨 So sánh kết quả:

### JavaScript (Cũ):
```
⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿
⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿
```

### Python AI (Mới):
```
⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⡿⠋⠑⠈⠛⢿
⣿⣿⣿⣿⡿⠿⠿⠿⠿⠻⠻⠟⠻⠻⣿⣿⣿⣿⣿⣿⠏⠀⢀⢮⡱⡀⢸
⣿⣿⡟⠁⢀⡠⠀⠀⠤⣐⠆⡖⠤⠄⠈⠂⠩⠉⣿⠋⠀⢤⢋⢦⠓⣍⠦
⣿⣿⠁⠐⠃⠀⣀⡀⠒⠈⢚⡜⡀⠀⡰⢩⠖⡀⠀⠀⠸⠬⣍⠶⡙⢦⡹
⣿⠃⠀⣠⠋⢠⢓⠆⠘⣄⡀⠪⡱⠀⣇⢫⠜⣱⠀⠄⢨⢳⠸⣬⢙⢦⡱
```

## 🐛 Troubleshooting:

### Lỗi "Module not found":
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### Lỗi "Permission denied":
```bash
# Windows
python -m pip install --user -r requirements.txt

# Linux/Mac
sudo pip install -r requirements.txt
```

### Server không khởi động:
- Kiểm tra port 5000 có bị chiếm không
- Thử chạy: `python ascii_server.py` để xem lỗi chi tiết

### Frontend không kết nối được:
- Đảm bảo Python server đang chạy trên port 5000
- Kiểm tra CORS settings
- Mở Developer Tools (F12) để xem lỗi

## 📊 API Endpoints:

- `POST /convert` - Convert image to ASCII art
- `GET /health` - Health check

## 🎯 Tham số tối ưu cho ảnh anime:

- **Width**: 38 (cố định)
- **Contrast**: 1.2-1.5
- **Brightness**: 1.0-1.2
- **Edge Enhancement**: Bật (mặc định)

## 🔍 Debug Mode:

Bật Debug Mode để xem:
- Thông tin xử lý ảnh
- Thời gian processing
- Kích thước ảnh trước/sau
- API response details

---

**🎉 Với Python AI backend, bạn sẽ có ASCII art đẹp và chi tiết như mong muốn!**
