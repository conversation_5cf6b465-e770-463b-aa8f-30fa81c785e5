# 🎨 ASCII Art Generator - Unicode Blocks

Ứng dụng web chuyển đổi ảnh thành ASCII art sử dụng Unicode Blocks, tạo ra những tác phẩm nghệ thuật văn bản đẹp mắt từ hình ảnh của bạn.

## ✨ Tính năng

- **Upload ảnh dễ dàng**: K<PERSON><PERSON> thả hoặc click để chọn ảnh
- **Nhiều kiểu Unicode Block**:
  - **Braille Patterns**: `⣿⣾⣽⣻⣟⣯⣷` - <PERSON> tiết cực cao, mịn màng như ảnh gốc (MẶC ĐỊNH)
  - **Density Blocks**: `█▉▊▋▌▍▎▏` - <PERSON> tiết cao, phù hợp cho ảnh phức tạp
  - **Shade Blocks**: `█▓▒░` - <PERSON><PERSON><PERSON> g<PERSON>, phù hợp cho ảnh có độ tương phản cao
  - **Mixed Blocks**: Kết hợp cả hai kiểu trên
- **Điều chỉnh linh hoạt**:
  - <PERSON><PERSON> rộng ASCII (20-200 ký tự)
  - <PERSON><PERSON> tương phản (0.5-2.0)
  - <PERSON><PERSON> sáng (0.5-2.0)
  - Cỡ chữ hiển thị (4-20px)
- **Xuất kết quả**: Copy vào clipboard hoặc tải về file .txt
- **Debug Mode**: Hiển thị thông tin chi tiết về quá trình chuyển đổi
- **Giao diện responsive**: Hoạt động tốt trên mọi thiết bị

## 🚀 Cách sử dụng

1. **Khởi chạy ứng dụng**:
   ```bash
   python -m http.server 8000
   ```
   Sau đó mở trình duyệt và truy cập `http://localhost:8000`

2. **Upload ảnh**:
   - Kéo thả ảnh vào vùng upload
   - Hoặc click vào vùng upload để chọn file
   - Hỗ trợ các định dạng: JPG, PNG, GIF, WebP

3. **Điều chỉnh tham số**:
   - **Độ rộng ASCII**: Số ký tự trên mỗi dòng (càng cao càng chi tiết)
   - **Độ tương phản**: Tăng để làm nổi bật sự khác biệt giữa vùng sáng/tối
   - **Độ sáng**: Điều chỉnh độ sáng tổng thể của ảnh
   - **Kiểu Unicode Block**: Chọn bộ ký tự phù hợp với ảnh

4. **Tạo ASCII Art**:
   - Click nút "🎨 Tạo ASCII Art"
   - Xem kết quả trong vùng hiển thị bên phải
   - (Tùy chọn) Click "🔍 Debug Mode" để xem thông tin chi tiết trong Console (F12)

5. **Xuất kết quả**:
   - **Copy**: Sao chép ASCII art vào clipboard
   - **Tải về**: Lưu thành file .txt
   - **Điều chỉnh cỠ chữ**: Thay đổi kích thước hiển thị

## 🎯 Mẹo sử dụng

- **Braille Patterns (Khuyến nghị)**: Độ rộng 40-80 ký tự, tuyệt vời cho ảnh anime/cartoon
- **Ảnh đơn giản**: Sử dụng Shade Blocks với độ rộng 40-60 ký tự
- **Ảnh phức tạp**: Sử dụng Density Blocks với độ rộng 80-120 ký tự
- **Ảnh tối**: Tăng độ sáng lên 1.2-1.5
- **Ảnh nhạt**: Tăng độ tương phản lên 1.3-1.8
- **Kết quả tốt nhất**: Sử dụng ảnh có độ tương phản cao, tránh ảnh quá mờ

## 🛠️ Công nghệ sử dụng

- **HTML5 Canvas**: Xử lý ảnh
- **JavaScript ES6+**: Logic chuyển đổi
- **CSS3**: Giao diện đẹp mắt với gradient và backdrop-filter
- **Unicode Blocks**: Ký tự đặc biệt để tạo ASCII art

## 📁 Cấu trúc project

```
ascii-art/
├── index.html      # Giao diện chính
├── style.css       # Styling và responsive design
├── script.js       # Logic chuyển đổi ASCII art
└── README.md       # Hướng dẫn sử dụng
```

## 🎨 Ví dụ kết quả

```
█████████████████████████████████████████████████████████████████████████████
█████████████████████████████████████████████████████████████████████████████
████████████████████████████▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓████████████████████████████
██████████████████████▓▓▓▓▓▓░░░░░░░░░░░░░░░░░░░░░░░░▓▓▓▓▓▓██████████████████
████████████████▓▓▓▓░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░▓▓▓▓████████████████
██████████▓▓▓▓░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░▓▓▓▓██████████
```

Enjoy creating beautiful ASCII art! 🎉
