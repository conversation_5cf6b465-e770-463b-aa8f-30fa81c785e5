<!DOCTYPE html>
<html>
<head>
    <title>🤖 AI Backend Demo - Advanced ASCII Art</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .demo { background: rgba(255,255,255,0.95); color: #333; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
        .ascii-demo { 
            font-family: 'Courier New', monospace; 
            font-size: 6px; 
            line-height: 0.8; 
            background: #1a1a1a; 
            color: #00ff00; 
            padding: 15px; 
            border-radius: 8px; 
            white-space: pre;
            overflow-x: auto;
            border: 2px solid #333;
            max-height: 400px;
        }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .status { padding: 15px; border-radius: 8px; margin: 10px 0; }
        .success { background: #d4edda; border-left: 4px solid #28a745; }
        .warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .info { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        .feature { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #667eea; }
        h1 { text-align: center; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .btn { background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px 5px; }
        .btn:hover { background: #218838; }
        .btn-primary { background: #667eea; }
        .btn-primary:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <h1>🤖 AI Enhanced ASCII Art Generator</h1>
    
    <div class="demo">
        <div class="success">
            <h3>✅ Python Backend đã sẵn sàng!</h3>
            <p><strong>Server Status:</strong> Running on http://localhost:5000</p>
            <p><strong>Frontend:</strong> http://localhost:8000</p>
            <p><strong>AI Processing:</strong> Enabled với PIL và advanced algorithms</p>
        </div>
    </div>

    <div class="demo">
        <h2>🚀 Tính năng AI Enhanced Processing:</h2>
        <div class="feature">
            <h4>🔬 Advanced Image Preprocessing:</h4>
            <ul>
                <li><strong>Brightness & Contrast Enhancement</strong>: Tự động điều chỉnh tối ưu</li>
                <li><strong>Edge Enhancement</strong>: Unsharp mask để làm nổi bật chi tiết</li>
                <li><strong>Noise Reduction</strong>: Gaussian blur để làm mượt</li>
                <li><strong>Smart Resizing</strong>: Lanczos interpolation chất lượng cao</li>
            </ul>
        </div>
        
        <div class="feature">
            <h4>🎨 Floyd-Steinberg Dithering:</h4>
            <ul>
                <li><strong>Error Diffusion</strong>: Phân bố lỗi pixel để tạo gradient mượt</li>
                <li><strong>Better Gradients</strong>: Chuyển tiếp màu sắc tự nhiên hơn</li>
                <li><strong>Reduced Banding</strong>: Giảm hiện tượng băng màu</li>
                <li><strong>Professional Quality</strong>: Kết quả như ví dụ bạn mong muốn</li>
            </ul>
        </div>
    </div>

    <div class="demo">
        <h2>📊 So sánh JavaScript vs Python AI:</h2>
        <div class="comparison">
            <div>
                <h3>JavaScript (Cũ):</h3>
                <div class="ascii-demo">⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿
⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿
⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿</div>
                <p><small>❌ Thiếu chi tiết, không có gradient</small></p>
            </div>
            <div>
                <h3>Python AI (Mới):</h3>
                <div class="ascii-demo">⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⡿⠋⠑⠈⠛⢿
⣿⣿⣿⣿⡿⠿⠿⠿⠿⠻⠻⠟⠻⠻⣿⣿⣿⣿⣿⣿⠏⠀⢀⢮⡱⡀⢸
⣿⣿⡟⠁⢀⡠⠀⠀⠤⣐⠆⡖⠤⠄⠈⠂⠩⠉⣿⠋⠀⢤⢋⢦⠓⣍⠦
⣿⣿⠁⠐⠃⠀⣀⡀⠒⠈⢚⡜⡀⠀⡰⢩⠖⡀⠀⠀⠸⠬⣍⠶⡙⢦⡹
⣿⠃⠀⣠⠋⢠⢓⠆⠘⣄⡀⠪⡱⠀⣇⢫⠜⣱⠀⠄⢨⢳⠸⣬⢙⢦⡱</div>
                <p><small>✅ Chi tiết cao, gradient mượt, giống ảnh gốc</small></p>
            </div>
        </div>
    </div>

    <div class="demo">
        <h2>⚙️ Cách sử dụng AI Backend:</h2>
        <div class="info">
            <ol>
                <li><strong>Đảm bảo Python server đang chạy</strong> (đã chạy rồi!)</li>
                <li><strong>Mở frontend:</strong> <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></li>
                <li><strong>Upload ảnh anime</strong> như ví dụ bạn gửi</li>
                <li><strong>Kiểm tra:</strong> "🤖 AI Enhanced Processing" đã được tích</li>
                <li><strong>Chọn:</strong> "Braille Patterns" (mặc định)</li>
                <li><strong>Điều chỉnh:</strong> Contrast 1.2-1.5, Brightness 1.0-1.2</li>
                <li><strong>Click:</strong> "🎨 Tạo ASCII Art"</li>
                <li><strong>Kết quả:</strong> ASCII art đẹp như mong muốn!</li>
            </ol>
        </div>
    </div>

    <div class="demo">
        <h2>🔧 Thuật toán AI Processing:</h2>
        <div class="feature">
            <h4>1. Image Preprocessing:</h4>
            <pre>Ảnh gốc → Grayscale → Brightness/Contrast → Edge Enhancement → Noise Reduction</pre>
        </div>
        
        <div class="feature">
            <h4>2. Floyd-Steinberg Dithering:</h4>
            <pre>For each pixel:
  error = old_pixel - new_pixel
  distribute error to neighbors:
    right: error × 7/16
    bottom-left: error × 3/16  
    bottom: error × 5/16
    bottom-right: error × 1/16</pre>
        </div>
        
        <div class="feature">
            <h4>3. Braille Mapping:</h4>
            <pre>2×4 pixel block → 8-bit value → Unicode Braille (U+2800 + value)</pre>
        </div>
    </div>

    <div class="demo">
        <h2>🎯 Tham số tối ưu cho ảnh anime:</h2>
        <div class="warning">
            <ul>
                <li><strong>Width:</strong> 38 (cố định)</li>
                <li><strong>Contrast:</strong> 1.3-1.5 (để làm nổi bật đường viền)</li>
                <li><strong>Brightness:</strong> 1.0-1.2 (giữ nguyên hoặc tăng nhẹ)</li>
                <li><strong>Edge Enhancement:</strong> Bật (mặc định)</li>
                <li><strong>Block Style:</strong> Braille Patterns</li>
            </ul>
        </div>
    </div>

    <div class="demo">
        <h2>🚀 Test ngay:</h2>
        <div style="text-align: center;">
            <a href="http://localhost:8000" target="_blank" class="btn btn-primary">🎨 Mở ASCII Art Generator</a>
            <a href="http://localhost:5000/health" target="_blank" class="btn">🔍 Check Server Status</a>
        </div>
        <p style="text-align: center;"><small>Python AI Backend đã sẵn sàng tạo ASCII art đẹp như bạn mong muốn!</small></p>
    </div>

    <div class="demo">
        <h2>🐛 Troubleshooting:</h2>
        <div class="warning">
            <h4>Nếu gặp lỗi "Không thể kết nối Python server":</h4>
            <ul>
                <li>Kiểm tra Python server có đang chạy không</li>
                <li>Thử refresh trang web</li>
                <li>Kiểm tra port 5000 có bị chiếm không</li>
                <li>Xem console (F12) để debug chi tiết</li>
            </ul>
        </div>
        
        <div class="info">
            <h4>Fallback mode:</h4>
            <p>Nếu Python server không hoạt động, bỏ tích "🤖 AI Enhanced Processing" để dùng JavaScript processing cơ bản.</p>
        </div>
    </div>
</body>
</html>
