#!/usr/bin/env python3
"""
Advanced ASCII Art Generator Server
Sử dụng OpenCV và PIL để xử lý ảnh chất lượng cao
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import base64
import io
import logging

app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedASCIIConverter:
    def __init__(self):
        # Braille Unicode patterns - more comprehensive set
        self.braille_chars = self._generate_braille_chars()
        
    def _generate_braille_chars(self):
        """Generate all 256 Braille characters"""
        chars = []
        for i in range(256):
            chars.append(chr(0x2800 + i))
        return chars
    
    def preprocess_image(self, image, contrast=1.0, brightness=1.0, enhance_edges=True):
        """
        Advanced image preprocessing using PIL
        """
        try:
            # Convert to grayscale
            if image.mode != 'L':
                gray_image = image.convert('L')
            else:
                gray_image = image.copy()

            # Apply brightness adjustment
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(gray_image)
                gray_image = enhancer.enhance(brightness)

            # Apply contrast adjustment
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(gray_image)
                gray_image = enhancer.enhance(contrast)

            # Edge enhancement if requested
            if enhance_edges:
                # Apply unsharp mask using PIL
                gray_image = gray_image.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))

            # Apply slight blur to reduce noise
            gray_image = gray_image.filter(ImageFilter.GaussianBlur(radius=0.5))

            # Convert to numpy array
            gray_array = np.array(gray_image)

            logger.info(f"Preprocessed image: {gray_array.shape}, dtype: {gray_array.dtype}")
            return gray_array

        except Exception as e:
            logger.error(f"Error in preprocessing: {e}")
            return np.array(image.convert('L'))
    
    def image_to_braille(self, image_array, target_width=38, target_height=None):
        """
        Convert image to Braille patterns with advanced dithering
        """
        try:
            height, width = image_array.shape
            
            # Calculate target height maintaining aspect ratio
            if target_height is None:
                aspect_ratio = height / width
                target_height = int(target_width * aspect_ratio * 2)  # *2 because Braille is 2x4
            
            # Ensure target_height is divisible by 4 (Braille height)
            target_height = (target_height // 4) * 4
            if target_height == 0:
                target_height = 4
                
            logger.info(f"Target dimensions: {target_width}x{target_height}")
            
            # Resize image using PIL
            pil_image = Image.fromarray(image_array)
            resized_pil = pil_image.resize((target_width * 2, target_height), Image.Resampling.LANCZOS)
            resized = np.array(resized_pil)
            
            # Apply Floyd-Steinberg dithering for better quality
            dithered = self._floyd_steinberg_dither(resized)
            
            # Convert to Braille
            braille_art = self._convert_to_braille_patterns(dithered, target_width)
            
            return braille_art
            
        except Exception as e:
            logger.error(f"Error in Braille conversion: {e}")
            return "Error converting image"
    
    def _floyd_steinberg_dither(self, image):
        """
        Apply Floyd-Steinberg dithering for better quality
        """
        try:
            img = image.astype(np.float32)
            height, width = img.shape
            
            for y in range(height):
                for x in range(width):
                    old_pixel = img[y, x]
                    new_pixel = 255 if old_pixel > 127 else 0
                    img[y, x] = new_pixel
                    
                    error = old_pixel - new_pixel
                    
                    # Distribute error to neighboring pixels
                    if x + 1 < width:
                        img[y, x + 1] += error * 7/16
                    if y + 1 < height:
                        if x > 0:
                            img[y + 1, x - 1] += error * 3/16
                        img[y + 1, x] += error * 5/16
                        if x + 1 < width:
                            img[y + 1, x + 1] += error * 1/16
            
            return img.astype(np.uint8)
            
        except Exception as e:
            logger.error(f"Error in dithering: {e}")
            return image
    
    def _convert_to_braille_patterns(self, image, target_width):
        """
        Convert dithered image to Braille patterns
        """
        try:
            height, width = image.shape
            result = []
            
            # Process in 2x4 blocks
            for y in range(0, height, 4):
                line = ""
                for x in range(0, width, 2):
                    # Get 2x4 block
                    block = image[y:y+4, x:x+2]
                    
                    # Convert to Braille character
                    braille_value = 0
                    
                    # Braille dot mapping (Unicode standard)
                    dot_positions = [
                        (0, 0, 0), (1, 0, 1), (2, 0, 2), (3, 0, 6),  # Left column
                        (0, 1, 3), (1, 1, 4), (2, 1, 5), (3, 1, 7)   # Right column
                    ]
                    
                    for row, col, bit in dot_positions:
                        if row < block.shape[0] and col < block.shape[1]:
                            if block[row, col] < 127:  # Dark pixel
                                braille_value |= (1 << bit)
                    
                    # Convert to Unicode character
                    braille_char = chr(0x2800 + braille_value)
                    line += braille_char
                
                # Ensure line is exactly target_width characters
                if len(line) > target_width:
                    line = line[:target_width]
                elif len(line) < target_width:
                    line += ' ' * (target_width - len(line))
                
                result.append(line)
            
            return '\n'.join(result)
            
        except Exception as e:
            logger.error(f"Error in Braille pattern conversion: {e}")
            return "Error in pattern conversion"

# Global converter instance
converter = AdvancedASCIIConverter()

@app.route('/convert', methods=['POST'])
def convert_image():
    """
    API endpoint to convert image to ASCII art
    """
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({'error': 'No image data provided'}), 400
        
        # Decode base64 image
        image_data = data['image'].split(',')[1]  # Remove data:image/...;base64,
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # Get parameters
        width = data.get('width', 38)
        contrast = data.get('contrast', 1.2)
        brightness = data.get('brightness', 1.0)
        enhance_edges = data.get('enhance_edges', True)
        
        logger.info(f"Processing image: {image.size}, params: w={width}, c={contrast}, b={brightness}")
        
        # Preprocess image
        processed_image = converter.preprocess_image(
            image, contrast=contrast, brightness=brightness, enhance_edges=enhance_edges
        )
        
        # Convert to Braille ASCII art
        ascii_art = converter.image_to_braille(processed_image, target_width=width)
        
        return jsonify({
            'ascii_art': ascii_art,
            'width': width,
            'lines': len(ascii_art.split('\n'))
        })
        
    except Exception as e:
        logger.error(f"Error in convert_image: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'ASCII Art Server is running'})

if __name__ == '__main__':
    print("🚀 Starting Advanced ASCII Art Server...")
    print("📡 Server will run on http://localhost:5000")
    print("🎨 Ready to process images with AI-enhanced algorithms!")
    app.run(host='0.0.0.0', port=5000, debug=True)
