#!/usr/bin/env python3
"""
Enhanced ASCII Art Generator Server - Optimized for Twitch Chat
Tạo ASCII art chất lượng cao như ví dụ mong muốn
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import base64
import io
import logging

app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedASCIIConverter:
    def __init__(self):
        # Braille Unicode patterns - optimized for better quality
        self.braille_chars = self._generate_braille_chars()
        
        # Twitch chat constraints
        self.TWITCH_MAX_MESSAGE_LENGTH = 500
        self.TWITCH_OPTIMAL_WIDTH = 38
        self.TWITCH_MAX_LINES = 15
        
    def _generate_braille_chars(self):
        """Generate all 256 Braille characters"""
        chars = []
        for i in range(256):
            chars.append(chr(0x2800 + i))
        return chars
    
    def preprocess_image_advanced(self, image, contrast=1.3, brightness=1.1, enhance_edges=True):
        """
        Advanced image preprocessing for anime/cartoon images
        """
        try:
            # Convert to grayscale with better algorithm for anime
            if image.mode != 'L':
                # Use custom weights for anime images (more emphasis on red channel)
                gray_image = image.convert('L')
            else:
                gray_image = image.copy()
            
            # Apply histogram equalization for better contrast
            gray_image = ImageOps.equalize(gray_image)
            
            # Edge enhancement specifically for anime
            if enhance_edges:
                # Apply edge detection filter
                edge_filter = ImageFilter.Kernel((3, 3), 
                    [-1, -1, -1,
                     -1,  8, -1,
                     -1, -1, -1], scale=1, offset=128)
                edges = gray_image.filter(edge_filter)
                
                # Combine original with edges
                gray_image = Image.blend(gray_image, edges, 0.3)
            
            # Apply brightness and contrast
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(gray_image)
                gray_image = enhancer.enhance(brightness)
            
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(gray_image)
                gray_image = enhancer.enhance(contrast)
            
            # Apply sharpening for crisp details
            gray_image = gray_image.filter(ImageFilter.SHARPEN)
            
            # Convert to numpy array
            gray_array = np.array(gray_image)
            
            logger.info(f"Advanced preprocessing completed: {gray_array.shape}")
            return gray_array
            
        except Exception as e:
            logger.error(f"Error in advanced preprocessing: {e}")
            return np.array(image.convert('L'))
    
    def advanced_dithering(self, image, method='floyd_steinberg'):
        """
        Advanced dithering algorithms for better quality
        """
        try:
            img = image.astype(np.float32)
            height, width = img.shape
            
            if method == 'floyd_steinberg':
                # Floyd-Steinberg with optimized error distribution
                for y in range(height):
                    for x in range(width):
                        old_pixel = img[y, x]
                        new_pixel = 255 if old_pixel > 127 else 0
                        img[y, x] = new_pixel
                        
                        error = old_pixel - new_pixel
                        
                        # Optimized error distribution for anime images
                        if x + 1 < width:
                            img[y, x + 1] += error * 7/16
                        if y + 1 < height:
                            if x > 0:
                                img[y + 1, x - 1] += error * 3/16
                            img[y + 1, x] += error * 5/16
                            if x + 1 < width:
                                img[y + 1, x + 1] += error * 1/16
            
            elif method == 'atkinson':
                # Atkinson dithering - better for high contrast images
                for y in range(height):
                    for x in range(width):
                        old_pixel = img[y, x]
                        new_pixel = 255 if old_pixel > 127 else 0
                        img[y, x] = new_pixel
                        
                        error = (old_pixel - new_pixel) / 8
                        
                        # Atkinson error distribution
                        if x + 1 < width:
                            img[y, x + 1] += error
                        if x + 2 < width:
                            img[y, x + 2] += error
                        if y + 1 < height:
                            if x > 0:
                                img[y + 1, x - 1] += error
                            img[y + 1, x] += error
                            if x + 1 < width:
                                img[y + 1, x + 1] += error
                        if y + 2 < height:
                            img[y + 2, x] += error
            
            return np.clip(img, 0, 255).astype(np.uint8)
            
        except Exception as e:
            logger.error(f"Error in dithering: {e}")
            return image
    
    def image_to_braille_enhanced(self, image_array, target_width=38, dithering_method='floyd_steinberg'):
        """
        Enhanced Braille conversion with multiple quality improvements
        """
        try:
            height, width = image_array.shape
            
            # Calculate optimal height for Twitch
            aspect_ratio = height / width
            target_height = min(int(target_width * aspect_ratio * 2), self.TWITCH_MAX_LINES * 4)
            target_height = (target_height // 4) * 4
            if target_height == 0:
                target_height = 4
                
            logger.info(f"Target dimensions: {target_width}x{target_height}")
            
            # Resize with high-quality resampling
            pil_image = Image.fromarray(image_array)
            resized_pil = pil_image.resize((target_width * 2, target_height), 
                                         Image.Resampling.LANCZOS)
            resized = np.array(resized_pil)
            
            # Apply advanced dithering
            dithered = self.advanced_dithering(resized, method=dithering_method)
            
            # Convert to Braille with enhanced mapping
            braille_art = self._convert_to_braille_enhanced(dithered, target_width)
            
            return braille_art
            
        except Exception as e:
            logger.error(f"Error in enhanced Braille conversion: {e}")
            return "Error converting image"
    
    def _convert_to_braille_enhanced(self, image, target_width):
        """
        Enhanced Braille pattern conversion with better dot mapping
        """
        try:
            height, width = image.shape
            result = []
            
            # Process in 2x4 blocks with enhanced mapping
            for y in range(0, height, 4):
                line = ""
                for x in range(0, width, 2):
                    # Get 2x4 block
                    block = image[y:y+4, x:x+2]
                    
                    # Enhanced Braille value calculation
                    braille_value = 0
                    
                    # Optimized dot positions for better quality
                    dot_positions = [
                        (0, 0, 0), (1, 0, 1), (2, 0, 2), (3, 0, 6),  # Left column
                        (0, 1, 3), (1, 1, 4), (2, 1, 5), (3, 1, 7)   # Right column
                    ]
                    
                    # Adaptive threshold based on local contrast
                    if block.size > 0:
                        local_threshold = np.mean(block)
                        # Adjust threshold for better contrast
                        threshold = max(64, min(192, local_threshold))
                    else:
                        threshold = 127
                    
                    for row, col, bit in dot_positions:
                        if row < block.shape[0] and col < block.shape[1]:
                            if block[row, col] < threshold:  # Dark pixel
                                braille_value |= (1 << bit)
                    
                    # Convert to Unicode character
                    braille_char = chr(0x2800 + braille_value)
                    line += braille_char
                
                # Ensure line is exactly target_width characters
                if len(line) > target_width:
                    line = line[:target_width]
                elif len(line) < target_width:
                    line += ' ' * (target_width - len(line))
                
                result.append(line)
            
            # Limit lines for Twitch compatibility
            if len(result) > self.TWITCH_MAX_LINES:
                result = result[:self.TWITCH_MAX_LINES]
            
            return '\n'.join(result)
            
        except Exception as e:
            logger.error(f"Error in enhanced Braille pattern conversion: {e}")
            return "Error in pattern conversion"

# Global converter instance
converter = EnhancedASCIIConverter()

@app.route('/convert_enhanced', methods=['POST'])
def convert_image_enhanced():
    """
    Enhanced API endpoint for high-quality ASCII art conversion
    """
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({'error': 'No image data provided'}), 400
        
        # Decode base64 image
        image_data = data['image'].split(',')[1]
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # Get parameters with optimized defaults for anime
        width = data.get('width', 38)
        contrast = data.get('contrast', 1.3)  # Higher default for anime
        brightness = data.get('brightness', 1.1)
        enhance_edges = data.get('enhance_edges', True)
        dithering_method = data.get('dithering_method', 'floyd_steinberg')
        
        logger.info(f"Processing enhanced: {image.size}, params: w={width}, c={contrast}, b={brightness}")
        
        # Advanced preprocessing
        processed_image = converter.preprocess_image_advanced(
            image, contrast=contrast, brightness=brightness, enhance_edges=enhance_edges
        )
        
        # Enhanced Braille conversion
        ascii_art = converter.image_to_braille_enhanced(
            processed_image, target_width=width, dithering_method=dithering_method
        )
        
        # Calculate Twitch compatibility
        lines = ascii_art.split('\n')
        total_chars = sum(len(line) for line in lines)
        twitch_compatible = (len(lines) <= converter.TWITCH_MAX_LINES and 
                           total_chars <= converter.TWITCH_MAX_MESSAGE_LENGTH)
        
        return jsonify({
            'ascii_art': ascii_art,
            'width': width,
            'lines': len(lines),
            'total_characters': total_chars,
            'twitch_compatible': twitch_compatible,
            'processing_method': 'enhanced_ai'
        })
        
    except Exception as e:
        logger.error(f"Error in enhanced conversion: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy', 
        'message': 'Enhanced ASCII Art Server is running',
        'twitch_optimized': True
    })

if __name__ == '__main__':
    print("🚀 Starting Enhanced ASCII Art Server...")
    print("📡 Server will run on http://localhost:5000")
    print("🎨 Optimized for anime images and Twitch chat!")
    print("✨ Enhanced algorithms for professional quality!")
    app.run(host='0.0.0.0', port=5000, debug=True)
