#!/usr/bin/env python3
"""
Enhanced Setup Script for ASCII Art Generator
Tự động cài đặt và khởi chạy enhanced server với tối ưu cho Twitch
"""

import subprocess
import sys
import os
import time
import webbrowser

def install_requirements():
    """Cài đặt Python packages"""
    print("📦 Installing Enhanced Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_python_version():
    """Kiểm tra Python version"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️  Warning: Python 3.8+ is recommended for best performance")
        return False
    return True

def start_enhanced_server():
    """Khởi chạy Enhanced ASCII Art server"""
    print("🚀 Starting Enhanced ASCII Art Server...")
    print("✨ Optimized for anime images and Twitch chat!")
    try:
        # Run the enhanced server
        subprocess.run([sys.executable, "enhanced_ascii_server.py"])
    except KeyboardInterrupt:
        print("\n👋 Enhanced server stopped by user")
    except Exception as e:
        print(f"❌ Error starting enhanced server: {e}")

def open_demo_page():
    """Mở demo page trong browser"""
    try:
        # Start simple HTTP server for frontend
        print("🌐 Starting frontend server...")
        subprocess.Popen([sys.executable, "-m", "http.server", "8000"], 
                        stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        time.sleep(2)  # Wait for server to start
        
        # Open browser
        webbrowser.open('http://localhost:8000')
        print("🎨 Frontend opened at http://localhost:8000")
        
    except Exception as e:
        print(f"⚠️  Could not auto-open browser: {e}")
        print("Please manually open http://localhost:8000")

def main():
    print("🎨 Enhanced ASCII Art Generator - Setup & Launch")
    print("🎮 Optimized for Twitch Chat & Anime Images")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        print("Please upgrade to Python 3.8+ for best experience")
        return
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found!")
        return
    
    # Install dependencies
    if not install_requirements():
        print("Failed to install dependencies. Please check your Python environment.")
        return
    
    print("\n🎉 Enhanced setup completed successfully!")
    print("📡 Enhanced server will start on http://localhost:5000")
    print("🌐 Frontend will be available at http://localhost:8000")
    print("🎮 Optimized for Twitch chat compatibility")
    print("\n✨ Features:")
    print("  • Advanced image preprocessing for anime")
    print("  • Floyd-Steinberg & Atkinson dithering")
    print("  • Twitch chat optimization")
    print("  • Professional quality output")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 60)
    
    # Open frontend in browser
    open_demo_page()
    
    # Wait a moment then start enhanced server
    time.sleep(3)
    start_enhanced_server()

if __name__ == "__main__":
    main()
