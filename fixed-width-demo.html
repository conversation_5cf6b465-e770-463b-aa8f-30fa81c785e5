<!DOCTYPE html>
<html>
<head>
    <title>Fixed Width ASCII Art Demo</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .demo { background: white; padding: 20px; border-radius: 10px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .ascii-demo { 
            font-family: 'Courier New', monospace; 
            font-size: 8px; 
            line-height: 0.8; 
            background: #1a1a1a; 
            color: #00ff00; 
            padding: 15px; 
            border-radius: 5px; 
            white-space: pre;
            overflow-x: auto;
            border: 1px solid #333;
        }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; }
        .success { background: #d4edda; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745; }
        .ruler { 
            font-family: 'Courier New', monospace; 
            font-size: 8px; 
            color: #666; 
            background: #f8f9fa; 
            padding: 5px; 
            border-radius: 3px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>📏 Fixed Width ASCII Art - 38 Ký Tự/Dòng</h1>
    
    <div class="demo">
        <h2>✨ Tính năng mới: Cố định độ rộng dòng</h2>
        <div class="success">
            <strong>✅ Đã thêm thành công!</strong>
            <ul>
                <li>Checkbox "Cố định 38 ký tự/dòng" đã được thêm</li>
                <li>Mặc định được bật và width slider = 38</li>
                <li>Mỗi dòng ASCII sẽ có đúng 38 ký tự (bao gồm khoảng trắng)</li>
                <li>Width slider bị disable khi fixed width được bật</li>
            </ul>
        </div>
    </div>

    <div class="demo">
        <h2>📏 Ruler - 38 ký tự:</h2>
        <div class="ruler">123456789012345678901234567890123456789</div>
        <div class="ruler">         1         2         3      38</div>
    </div>

    <div class="demo">
        <h2>🎯 Ví dụ kết quả với Fixed Width:</h2>
        <p><strong>Mỗi dòng dưới đây có đúng 38 ký tự:</strong></p>
        <div class="ascii-demo">⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⡿⠋⠑⠈⠛⢿   
⣿⣿⣿⣿⡿⠿⠿⠿⠿⠻⠻⠟⠻⠻⣿⣿⣿⣿⣿⣿⠏⠀⢀⢮⡱⡀⢸   
⣿⣿⡟⠁⢀⡠⠀⠀⠤⣐⠆⡖⠤⠄⠈⠂⠩⠉⣿⠋⠀⢤⢋⢦⠓⣍⠦   
⣿⣿⠁⠐⠃⠀⣀⡀⠒⠈⢚⡜⡀⠀⡰⢩⠖⡀⠀⠀⠸⠬⣍⠶⡙⢦⡹   
⣿⠃⠀⣠⠋⢠⢓⠆⠘⣄⡀⠪⡱⠀⣇⢫⠜⣱⠀⠄⢨⢳⠸⣬⢙⢦⡱   </div>
        <p><small>* Khoảng trắng ở cuối dòng được thêm tự động để đạt 38 ký tự</small></p>
    </div>

    <div class="demo">
        <h2>⚙️ Cách sử dụng:</h2>
        <ol>
            <li><strong>Mở ứng dụng:</strong> <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></li>
            <li><strong>Upload ảnh:</strong> Chọn ảnh bạn muốn chuyển đổi</li>
            <li><strong>Kiểm tra checkbox:</strong> "Cố định 38 ký tự/dòng" đã được tích sẵn</li>
            <li><strong>Width slider:</strong> Sẽ bị disable và cố định ở 38</li>
            <li><strong>Tạo ASCII Art:</strong> Kết quả sẽ có mỗi dòng đúng 38 ký tự</li>
        </ol>
    </div>

    <div class="demo">
        <h2>🔧 Thuật toán Fixed Width:</h2>
        <div class="highlight">
            <strong>Cách hoạt động:</strong>
            <ul>
                <li><strong>Nếu dòng ngắn hơn 38 ký tự:</strong> Thêm khoảng trắng vào cuối</li>
                <li><strong>Nếu dòng dài hơn 38 ký tự:</strong> Cắt bớt về đúng 38 ký tự</li>
                <li><strong>Loại bỏ trailing spaces:</strong> Trước khi format</li>
                <li><strong>Áp dụng cho tất cả:</strong> Braille patterns và Unicode blocks</li>
            </ul>
        </div>
    </div>

    <div class="demo">
        <h2>🎨 So sánh chế độ:</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f8f9fa;">
                <th style="padding: 10px; border: 1px solid #ddd;">Chế độ</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Width Slider</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Độ dài dòng</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Khoảng trắng</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Fixed Width ON</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">Disabled (38)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">Đúng 38 ký tự</td>
                <td style="padding: 10px; border: 1px solid #ddd;">Được thêm tự động</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Fixed Width OFF</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">Enabled (20-200)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">Theo slider</td>
                <td style="padding: 10px; border: 1px solid #ddd;">Tự nhiên</td>
            </tr>
        </table>
    </div>

    <div class="demo">
        <h2>💡 Lợi ích của Fixed Width:</h2>
        <ul>
            <li><strong>Nhất quán:</strong> Tất cả dòng có cùng độ dài</li>
            <li><strong>Dễ copy/paste:</strong> Format đều đặn</li>
            <li><strong>Tương thích:</strong> Hoạt động tốt trong text editor</li>
            <li><strong>Chuyên nghiệp:</strong> Trông gọn gàng hơn</li>
        </ul>
    </div>

    <div class="demo">
        <h2>🚀 Test ngay:</h2>
        <p><a href="http://localhost:8000" target="_blank" style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;">🎨 Mở ASCII Art Generator</a></p>
        <p><small>Fixed Width 38 ký tự đã được bật mặc định!</small></p>
    </div>
</body>
</html>
