class ASCIIArtGenerator {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.currentImage = null;
        this.debugMode = false;
        this.useAdvancedProcessing = true; // Use Python backend by default

        // Unicode block characters for different styles
        this.blockStyles = {
            density: ['█', '▉', '▊', '▋', '▌', '▍', '▎', '▏', ' '],
            shade: ['█', '▓', '▒', '░', ' '],
            mixed: ['█', '▉', '▊', '▋', '▌', '▍', '▎', '▏', '▓', '▒', '░', ' '],
            braille: ['⣿', '⣾', '⣽', '⣻', '⣟', '⣯', '⣷', '⣶', '⣴', '⣲', '⣱', '⣰', '⣠', '⣀', '⠀']
        };

        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        const generateBtn = document.getElementById('generateBtn');
        const debugBtn = document.getElementById('debugBtn');
        const copyBtn = document.getElementById('copyBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        
        // Slider controls
        const widthSlider = document.getElementById('widthSlider');
        const contrastSlider = document.getElementById('contrastSlider');
        const brightnessSlider = document.getElementById('brightnessSlider');
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        
        // Upload area events
        uploadArea.addEventListener('click', () => imageInput.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        
        // File input
        imageInput.addEventListener('change', this.handleFileSelect.bind(this));
        
        // Generate button
        generateBtn.addEventListener('click', this.generateASCII.bind(this));

        // Debug button
        debugBtn.addEventListener('click', this.toggleDebugMode.bind(this));
        
        // Copy and download
        copyBtn.addEventListener('click', this.copyToClipboard.bind(this));
        downloadBtn.addEventListener('click', this.downloadASCII.bind(this));

        // Copy for Twitch
        const copyTwitchBtn = document.getElementById('copyTwitchBtn');
        if (copyTwitchBtn) {
            copyTwitchBtn.addEventListener('click', this.copyForTwitch.bind(this));
        }
        
        // Slider updates
        widthSlider.addEventListener('input', (e) => {
            document.getElementById('widthValue').textContent = e.target.value;
        });

        // Auto-adjust width based on block style
        document.getElementById('blockStyle').addEventListener('change', (e) => {
            const style = e.target.value;
            const fixedWidth = document.getElementById('fixedWidth').checked;

            if (fixedWidth) {
                widthSlider.value = 38;
                document.getElementById('widthValue').textContent = '38';
            } else if (style === 'braille') {
                widthSlider.value = 60;
                document.getElementById('widthValue').textContent = '60';
            } else {
                widthSlider.value = 80;
                document.getElementById('widthValue').textContent = '80';
            }
        });

        // Fixed width checkbox handler
        document.getElementById('fixedWidth').addEventListener('change', (e) => {
            if (e.target.checked) {
                widthSlider.value = 38;
                document.getElementById('widthValue').textContent = '38';
                widthSlider.disabled = true;
            } else {
                widthSlider.disabled = false;
                const style = document.getElementById('blockStyle').value;
                if (style === 'braille') {
                    widthSlider.value = 60;
                    document.getElementById('widthValue').textContent = '60';
                } else {
                    widthSlider.value = 80;
                    document.getElementById('widthValue').textContent = '80';
                }
            }
        });
        
        contrastSlider.addEventListener('input', (e) => {
            document.getElementById('contrastValue').textContent = parseFloat(e.target.value).toFixed(1);
        });
        
        brightnessSlider.addEventListener('input', (e) => {
            document.getElementById('brightnessValue').textContent = parseFloat(e.target.value).toFixed(1);
        });
        
        fontSizeSlider.addEventListener('input', (e) => {
            const fontSize = e.target.value + 'px';
            document.getElementById('fontSizeValue').textContent = fontSize;
            document.getElementById('asciiResult').style.fontSize = fontSize;
        });
    }
    
    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }
    
    handleDragLeave(e) {
        e.currentTarget.classList.remove('dragover');
    }
    
    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith('image/')) {
            this.loadImage(files[0]);
        }
    }
    
    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file && file.type.startsWith('image/')) {
            this.loadImage(file);
        }
    }
    
    loadImage(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.currentImage = img;
                this.showControls();
                this.showPreview(e.target.result);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    
    showControls() {
        document.getElementById('controlsSection').style.display = 'block';
        document.getElementById('debugBtn').style.display = 'inline-block';

        // Initialize fixed width state
        const fixedWidthCheckbox = document.getElementById('fixedWidth');
        const widthSlider = document.getElementById('widthSlider');
        if (fixedWidthCheckbox.checked) {
            widthSlider.disabled = true;
            widthSlider.value = 38;
            document.getElementById('widthValue').textContent = '38';
        }
    }

    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        const debugBtn = document.getElementById('debugBtn');
        debugBtn.textContent = this.debugMode ? '🔍 Debug ON' : '🔍 Debug Mode';
        debugBtn.style.background = this.debugMode ?
            'linear-gradient(135deg, #28a745 0%, #20c997 100%)' :
            'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
    }
    
    showPreview(imageSrc) {
        const previewImg = document.getElementById('previewImage');
        previewImg.src = imageSrc;
        document.getElementById('resultSection').style.display = 'block';
    }
    
    async generateASCII() {
        if (!this.currentImage) return;

        const useEnhanced = document.getElementById('enhancedProcessing').checked;
        const width = parseInt(document.getElementById('widthSlider').value);
        const contrast = parseFloat(document.getElementById('contrastSlider').value);
        const brightness = parseFloat(document.getElementById('brightnessSlider').value);
        const ditheringMethod = document.getElementById('ditheringMethod').value;

        // Show loading state
        const generateBtn = document.getElementById('generateBtn');
        const originalText = generateBtn.textContent;
        generateBtn.textContent = '⏳ Đang xử lý...';
        generateBtn.disabled = true;

        try {
            let result;

            if (useEnhanced) {
                // Use enhanced Python backend
                result = await this.generateWithEnhancedBackend(width, contrast, brightness, ditheringMethod);
            } else {
                // Fallback to basic processing
                result = {
                    ascii_art: 'Enhanced processing disabled. Please enable for best quality.',
                    twitch_compatible: false,
                    lines: 1,
                    total_characters: 0
                };
            }

            // Display result
            document.getElementById('asciiResult').textContent = result.ascii_art;

            // Update Twitch compatibility info
            this.updateTwitchInfo(result);

        } catch (error) {
            console.error('Error generating ASCII art:', error);
            document.getElementById('asciiResult').textContent = 'Lỗi: ' + error.message;
        } finally {
            // Restore button state
            generateBtn.textContent = originalText;
            generateBtn.disabled = false;
        }
    }

    async generateWithEnhancedBackend(width, contrast, brightness, ditheringMethod) {
        try {
            // Convert image to base64
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = this.currentImage.width;
            canvas.height = this.currentImage.height;
            ctx.drawImage(this.currentImage, 0, 0);
            const imageData = canvas.toDataURL('image/png');

            if (this.debugMode) {
                console.log('🚀 Using Enhanced Python backend');
                console.log('Parameters:', { width, contrast, brightness, ditheringMethod });
            }

            // Call enhanced Python backend
            const response = await fetch('http://localhost:5000/convert_enhanced', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image: imageData,
                    width: width,
                    contrast: contrast,
                    brightness: brightness,
                    enhance_edges: true,
                    dithering_method: ditheringMethod
                })
            });

            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }

            const result = await response.json();

            if (this.debugMode) {
                console.log('✅ Enhanced backend response:', result);
            }

            return result;

        } catch (error) {
            console.error('Enhanced backend error:', error);
            throw new Error('Không thể kết nối Enhanced Python server. Hãy đảm bảo server đang chạy.');
        }
    }

    generateWithJavaScript(width, contrast, brightness, blockStyle) {
        // Calculate height to maintain aspect ratio
        const aspectRatio = this.currentImage.height / this.currentImage.width;
        const height = Math.floor(width * aspectRatio * 0.5); // 0.5 to account for character height/width ratio

        // Set canvas size
        this.canvas.width = width;
        this.canvas.height = height;

        // Clear canvas first
        this.ctx.clearRect(0, 0, width, height);

        // Set canvas context properties for better quality
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';

        // Draw image on canvas with proper scaling
        this.ctx.drawImage(this.currentImage, 0, 0, width, height);

        // Get image data
        const imageData = this.ctx.getImageData(0, 0, width, height);
        const pixels = imageData.data;

        // Debug: Log canvas and image data info
        if (this.debugMode) {
            console.log('=== JAVASCRIPT DEBUG INFO ===');
            console.log('Original image size:', this.currentImage.width, 'x', this.currentImage.height);
            console.log('Canvas size:', width, 'x', height);
            console.log('Image data length:', pixels.length);
            console.log('Expected pixels:', width * height * 4);
            console.log('Aspect ratio:', aspectRatio);
            console.log('Block style:', blockStyle);
            console.log('Settings - Contrast:', contrast, 'Brightness:', brightness);
        }

        // Convert to ASCII
        return this.convertToASCII(pixels, width, height, contrast, brightness, blockStyle);
    }

    updateTwitchInfo(result) {
        const twitchInfo = document.getElementById('twitchInfo');
        const twitchStatus = document.getElementById('twitchStatus');
        const charCount = document.getElementById('charCount');
        const lineCount = document.getElementById('lineCount');

        if (!twitchInfo || !twitchStatus || !charCount || !lineCount) {
            return; // Elements not found, skip update
        }

        twitchInfo.style.display = 'flex';

        if (result.twitch_compatible) {
            twitchStatus.textContent = '✅ Twitch Compatible';
            twitchStatus.className = 'twitch-status compatible';
        } else {
            twitchStatus.textContent = '⚠️ Too large for Twitch';
            twitchStatus.className = 'twitch-status incompatible';
        }

        charCount.textContent = `${result.total_characters || 0}/500 chars`;
        lineCount.textContent = `${result.lines || 0}/15 lines`;
    }
    
    convertToASCII(pixels, width, height, contrast, brightness, blockStyle) {
        if (blockStyle === 'braille') {
            return this.convertToBraille(pixels, width, height, contrast, brightness);
        }

        const blocks = this.blockStyles[blockStyle];
        let ascii = '';
        let pixelErrors = 0;
        const fixedWidth = document.getElementById('fixedWidth').checked;
        const targetLineLength = 38;

        if (this.debugMode) {
            console.log('Converting to ASCII with blocks:', blocks);
            console.log('Fixed width mode:', fixedWidth);
        }

        for (let y = 0; y < height; y++) {
            let line = '';
            for (let x = 0; x < width; x++) {
                const pixelIndex = (y * width + x) * 4;

                // Check if pixel index is valid
                if (pixelIndex + 3 >= pixels.length) {
                    pixelErrors++;
                    if (this.debugMode && pixelErrors <= 5) {
                        console.warn(`Pixel index out of bounds at (${x}, ${y}): ${pixelIndex} >= ${pixels.length}`);
                    }
                    line += blocks[blocks.length - 1]; // Use lightest character as fallback
                    continue;
                }

                // Get RGB values with bounds checking
                const r = Math.max(0, Math.min(255, pixels[pixelIndex] || 0));
                const g = Math.max(0, Math.min(255, pixels[pixelIndex + 1] || 0));
                const b = Math.max(0, Math.min(255, pixels[pixelIndex + 2] || 0));

                // Calculate grayscale value using standard luminance formula
                let gray = (r * 0.299 + g * 0.587 + b * 0.114);

                // Apply brightness and contrast
                gray = gray * brightness;
                gray = ((gray - 128) * contrast) + 128;
                gray = Math.max(0, Math.min(255, gray));

                // Map to block character with better precision
                const normalizedGray = gray / 255;
                const blockIndex = Math.floor(normalizedGray * (blocks.length - 1));
                const charIndex = Math.max(0, Math.min(blocks.length - 1, blocks.length - 1 - blockIndex));
                line += blocks[charIndex];
            }

            // Apply fixed width formatting if enabled
            if (fixedWidth) {
                line = this.formatFixedWidth(line, targetLineLength);
            }

            ascii += line + '\n';
        }

        if (this.debugMode && pixelErrors > 0) {
            console.warn(`Total pixel errors: ${pixelErrors}`);
        }

        return ascii;
    }

    convertToBraille(pixels, width, height, contrast, brightness) {
        let ascii = '';
        const threshold = 128; // Threshold for black/white decision
        const fixedWidth = document.getElementById('fixedWidth').checked;
        const targetLineLength = 38;

        if (this.debugMode) {
            console.log('Converting to Braille patterns');
            console.log('Canvas size for Braille:', width, 'x', height);
            console.log('Fixed width mode:', fixedWidth);
        }

        // Process in 2x4 blocks (Braille character size)
        for (let y = 0; y < height; y += 4) {
            let line = '';
            for (let x = 0; x < width; x += 2) {
                let brailleValue = 0;

                // Braille dot positions (Unicode Braille pattern)
                // 1 4
                // 2 5
                // 3 6
                // 7 8
                const dotPositions = [
                    [0, 0], [0, 1], [1, 0], [1, 1], // dots 1,2,3,7
                    [2, 0], [2, 1], [3, 0], [3, 1]  // dots 4,5,6,8
                ];

                // Check each dot position
                for (let i = 0; i < 8; i++) {
                    const dotX = x + (i >= 4 ? 1 : 0);
                    const dotY = y + dotPositions[i][0];

                    if (dotX < width && dotY < height) {
                        const pixelIndex = (dotY * width + dotX) * 4;

                        if (pixelIndex + 2 < pixels.length) {
                            const r = pixels[pixelIndex] || 0;
                            const g = pixels[pixelIndex + 1] || 0;
                            const b = pixels[pixelIndex + 2] || 0;

                            // Calculate grayscale
                            let gray = (r * 0.299 + g * 0.587 + b * 0.114);

                            // Apply brightness and contrast
                            gray = gray * brightness;
                            gray = ((gray - 128) * contrast) + 128;
                            gray = Math.max(0, Math.min(255, gray));

                            // If pixel is dark enough, set the corresponding Braille dot
                            if (gray < threshold) {
                                brailleValue |= (1 << i);
                            }
                        }
                    }
                }

                // Convert to Braille Unicode character
                const brailleChar = String.fromCharCode(0x2800 + brailleValue);
                line += brailleChar;
            }

            // Apply fixed width formatting if enabled
            if (fixedWidth) {
                line = this.formatFixedWidth(line, targetLineLength);
            }

            ascii += line + '\n';
        }

        return ascii;
    }

    formatFixedWidth(line, targetLength) {
        // Remove trailing spaces first
        line = line.trimEnd();

        if (line.length > targetLength) {
            // Truncate if too long
            return line.substring(0, targetLength);
        } else if (line.length < targetLength) {
            // Pad with spaces if too short
            return line + ' '.repeat(targetLength - line.length);
        }

        return line;
    }

    copyToClipboard() {
        const asciiText = document.getElementById('asciiResult').textContent;
        navigator.clipboard.writeText(asciiText).then(() => {
            const copyBtn = document.getElementById('copyBtn');
            const originalText = copyBtn.textContent;
            copyBtn.textContent = '✅ Đã copy!';
            setTimeout(() => {
                copyBtn.textContent = originalText;
            }, 2000);
        });
    }

    copyForTwitch() {
        const asciiText = document.getElementById('asciiResult').textContent;
        const lines = asciiText.split('\n');

        // Optimize for Twitch if needed
        let twitchText = asciiText;
        if (lines.length > 15) {
            twitchText = lines.slice(0, 15).join('\n');
        }

        navigator.clipboard.writeText(twitchText).then(() => {
            const copyTwitchBtn = document.getElementById('copyTwitchBtn');
            const originalText = copyTwitchBtn.textContent;
            copyTwitchBtn.textContent = '✅ Copied for Twitch!';
            setTimeout(() => {
                copyTwitchBtn.textContent = originalText;
            }, 2000);
        });
    }
    
    downloadASCII() {
        const asciiText = document.getElementById('asciiResult').textContent;
        const blob = new Blob([asciiText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'ascii-art.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ASCIIArtGenerator();
});
