/**
 * Enhanced ASCII Art Generator - Fixed and Optimized
 * Organized code structure with proper error handling
 */

class EnhancedASCIIGenerator {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.currentImage = null;
        this.debugMode = false;
        
        // Twitch chat constraints
        this.TWITCH_MAX_MESSAGE_LENGTH = 500;
        this.TWITCH_OPTIMAL_WIDTH = 38;
        this.TWITCH_MAX_LINES = 15;
        
        this.initializeEventListeners();
    }
    
    // ==================== EVENT LISTENERS ====================
    
    initializeEventListeners() {
        this.initializeUploadEvents();
        this.initializeControlButtons();
        this.initializeSliders();
        this.initializeCheckboxes();
    }
    
    initializeUploadEvents() {
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        
        if (uploadArea && imageInput) {
            uploadArea.addEventListener('click', () => imageInput.click());
            uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
            uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
            uploadArea.addEventListener('drop', this.handleDrop.bind(this));
            imageInput.addEventListener('change', this.handleFileSelect.bind(this));
        }
    }
    
    initializeControlButtons() {
        const generateBtn = document.getElementById('generateBtn');
        const debugBtn = document.getElementById('debugBtn');
        const copyBtn = document.getElementById('copyBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const copyTwitchBtn = document.getElementById('copyTwitchBtn');
        
        if (generateBtn) generateBtn.addEventListener('click', this.generateASCII.bind(this));
        if (debugBtn) debugBtn.addEventListener('click', this.toggleDebugMode.bind(this));
        if (copyBtn) copyBtn.addEventListener('click', this.copyToClipboard.bind(this));
        if (downloadBtn) downloadBtn.addEventListener('click', this.downloadASCII.bind(this));
        if (copyTwitchBtn) copyTwitchBtn.addEventListener('click', this.copyForTwitch.bind(this));
    }
    
    initializeSliders() {
        // Width slider
        const widthSlider = document.getElementById('widthSlider');
        if (widthSlider) {
            widthSlider.addEventListener('input', (e) => {
                const widthValue = document.getElementById('widthValue');
                if (widthValue) widthValue.textContent = e.target.value;
            });
        }
        
        // Contrast slider
        const contrastSlider = document.getElementById('contrastSlider');
        if (contrastSlider) {
            contrastSlider.addEventListener('input', (e) => {
                const contrastValue = document.getElementById('contrastValue');
                if (contrastValue) contrastValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }
        
        // Brightness slider
        const brightnessSlider = document.getElementById('brightnessSlider');
        if (brightnessSlider) {
            brightnessSlider.addEventListener('input', (e) => {
                const brightnessValue = document.getElementById('brightnessValue');
                if (brightnessValue) brightnessValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }
        
        // Font size slider
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        if (fontSizeSlider) {
            fontSizeSlider.addEventListener('input', (e) => {
                const fontSize = e.target.value + 'px';
                const fontSizeValue = document.getElementById('fontSizeValue');
                const asciiResult = document.getElementById('asciiResult');
                
                if (fontSizeValue) fontSizeValue.textContent = fontSize;
                if (asciiResult) asciiResult.style.fontSize = fontSize;
            });
        }
    }
    
    initializeCheckboxes() {
        const fixedWidthCheckbox = document.getElementById('fixedWidth');
        if (fixedWidthCheckbox) {
            fixedWidthCheckbox.addEventListener('change', (e) => {
                this.updateWidthSliderState(e.target.checked);
            });
        }
    }
    
    // ==================== UI STATE MANAGEMENT ====================
    
    updateWidthSliderState(isFixed) {
        const widthSlider = document.getElementById('widthSlider');
        const widthValue = document.getElementById('widthValue');
        const widthHint = document.querySelector('.width-hint');
        
        if (!widthSlider || !widthValue) return;
        
        if (isFixed) {
            // Twitch optimal mode - cố định 38 ký tự
            widthSlider.value = this.TWITCH_OPTIMAL_WIDTH;
            widthValue.textContent = this.TWITCH_OPTIMAL_WIDTH.toString();
            widthSlider.disabled = true;
            widthSlider.style.opacity = '0.5';
            if (widthHint) widthHint.style.display = 'none';
        } else {
            // Free adjustment mode - cho phép điều chỉnh 20-200
            widthSlider.disabled = false;
            widthSlider.style.opacity = '1';
            widthSlider.value = 60; // Default cho enhanced processing
            widthValue.textContent = '60';
            if (widthHint) widthHint.style.display = 'block';
        }
    }
    
    showControls() {
        const controlsSection = document.getElementById('controlsSection');
        const debugBtn = document.getElementById('debugBtn');
        
        if (controlsSection) controlsSection.style.display = 'block';
        if (debugBtn) debugBtn.style.display = 'inline-block';

        // Initialize fixed width state
        const fixedWidthCheckbox = document.getElementById('fixedWidth');
        if (fixedWidthCheckbox) {
            this.updateWidthSliderState(fixedWidthCheckbox.checked);
        }
    }
    
    showPreview(imageSrc) {
        const previewImg = document.getElementById('previewImage');
        const resultSection = document.getElementById('resultSection');
        
        if (previewImg) previewImg.src = imageSrc;
        if (resultSection) resultSection.style.display = 'block';
    }
    
    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        const debugBtn = document.getElementById('debugBtn');
        
        if (debugBtn) {
            debugBtn.textContent = this.debugMode ? '🔍 Debug ON' : '🔍 Debug Mode';
            debugBtn.style.background = this.debugMode ? 
                'linear-gradient(135deg, #28a745 0%, #20c997 100%)' : 
                'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
        }
    }
    
    // ==================== FILE HANDLING ====================
    
    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }
    
    handleDragLeave(e) {
        e.currentTarget.classList.remove('dragover');
    }
    
    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith('image/')) {
            this.loadImage(files[0]);
        }
    }
    
    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file && file.type.startsWith('image/')) {
            this.loadImage(file);
        }
    }
    
    loadImage(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.currentImage = img;
                this.showControls();
                this.showPreview(e.target.result);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    
    // ==================== ASCII GENERATION ====================
    
    async generateASCII() {
        if (!this.currentImage) return;
        
        const useEnhanced = document.getElementById('enhancedProcessing')?.checked ?? true;
        const width = parseInt(document.getElementById('widthSlider')?.value ?? 38);
        const contrast = parseFloat(document.getElementById('contrastSlider')?.value ?? 1.3);
        const brightness = parseFloat(document.getElementById('brightnessSlider')?.value ?? 1.1);
        const ditheringMethod = document.getElementById('ditheringMethod')?.value ?? 'floyd_steinberg';
        
        // Show loading state
        const generateBtn = document.getElementById('generateBtn');
        const originalText = generateBtn?.textContent ?? '';
        if (generateBtn) {
            generateBtn.textContent = '⏳ Đang xử lý...';
            generateBtn.disabled = true;
        }
        
        try {
            let result;
            
            if (useEnhanced) {
                result = await this.generateWithEnhancedBackend(width, contrast, brightness, ditheringMethod);
            } else {
                result = {
                    ascii_art: 'Enhanced processing disabled. Please enable for best quality.',
                    twitch_compatible: false,
                    lines: 1,
                    total_characters: 0
                };
            }
            
            // Display result
            const asciiResult = document.getElementById('asciiResult');
            if (asciiResult) asciiResult.textContent = result.ascii_art;
            
            // Update Twitch compatibility info
            this.updateTwitchInfo(result);
            
        } catch (error) {
            console.error('Error generating ASCII art:', error);
            const asciiResult = document.getElementById('asciiResult');
            if (asciiResult) asciiResult.textContent = 'Lỗi: ' + error.message;
        } finally {
            // Restore button state
            if (generateBtn) {
                generateBtn.textContent = originalText;
                generateBtn.disabled = false;
            }
        }
    }
    
    async generateWithEnhancedBackend(width, contrast, brightness, ditheringMethod) {
        try {
            // Convert image to base64
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = this.currentImage.width;
            canvas.height = this.currentImage.height;
            ctx.drawImage(this.currentImage, 0, 0);
            const imageData = canvas.toDataURL('image/png');
            
            if (this.debugMode) {
                console.log('🚀 Using Enhanced Python backend');
                console.log('Parameters:', { width, contrast, brightness, ditheringMethod });
            }
            
            // Call enhanced Python backend
            const response = await fetch('http://localhost:5000/convert_enhanced', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    image: imageData,
                    width: width,
                    contrast: contrast,
                    brightness: brightness,
                    enhance_edges: true,
                    dithering_method: ditheringMethod
                })
            });
            
            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (this.debugMode) {
                console.log('✅ Enhanced backend response:', result);
            }
            
            return result;
            
        } catch (error) {
            console.error('Enhanced backend error:', error);
            throw new Error('Không thể kết nối Enhanced Python server. Hãy đảm bảo server đang chạy.');
        }
    }
    
    // ==================== UI UPDATES ====================
    
    updateTwitchInfo(result) {
        const twitchInfo = document.getElementById('twitchInfo');
        const twitchStatus = document.getElementById('twitchStatus');
        const charCount = document.getElementById('charCount');
        const lineCount = document.getElementById('lineCount');
        
        if (!twitchInfo || !twitchStatus || !charCount || !lineCount) return;
        
        twitchInfo.style.display = 'flex';
        
        if (result.twitch_compatible) {
            twitchStatus.textContent = '✅ Twitch Compatible';
            twitchStatus.className = 'twitch-status compatible';
        } else {
            twitchStatus.textContent = '⚠️ Too large for Twitch';
            twitchStatus.className = 'twitch-status incompatible';
        }
        
        charCount.textContent = `${result.total_characters || 0}/${this.TWITCH_MAX_MESSAGE_LENGTH} chars`;
        lineCount.textContent = `${result.lines || 0}/${this.TWITCH_MAX_LINES} lines`;
    }
    
    // ==================== COPY/DOWNLOAD FUNCTIONS ====================
    
    copyToClipboard() {
        const asciiResult = document.getElementById('asciiResult');
        if (!asciiResult) return;
        
        const asciiText = asciiResult.textContent;
        navigator.clipboard.writeText(asciiText).then(() => {
            const copyBtn = document.getElementById('copyBtn');
            if (copyBtn) {
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '✅ Đã copy!';
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                }, 2000);
            }
        });
    }
    
    copyForTwitch() {
        const asciiResult = document.getElementById('asciiResult');
        if (!asciiResult) return;
        
        const asciiText = asciiResult.textContent;
        const lines = asciiText.split('\n');
        
        // Optimize for Twitch if needed
        let twitchText = asciiText;
        if (lines.length > this.TWITCH_MAX_LINES) {
            twitchText = lines.slice(0, this.TWITCH_MAX_LINES).join('\n');
        }
        
        navigator.clipboard.writeText(twitchText).then(() => {
            const copyTwitchBtn = document.getElementById('copyTwitchBtn');
            if (copyTwitchBtn) {
                const originalText = copyTwitchBtn.textContent;
                copyTwitchBtn.textContent = '✅ Copied for Twitch!';
                setTimeout(() => {
                    copyTwitchBtn.textContent = originalText;
                }, 2000);
            }
        });
    }
    
    downloadASCII() {
        const asciiResult = document.getElementById('asciiResult');
        if (!asciiResult) return;
        
        const asciiText = asciiResult.textContent;
        const blob = new Blob([asciiText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'enhanced-ascii-art.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Initialize the enhanced application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedASCIIGenerator();
});
