#!/usr/bin/env python3
"""
Setup script for ASCII Art Python Server
Tự động cài đặt dependencies và khởi chạy server
"""

import subprocess
import sys
import os
import time

def install_requirements():
    """Cài đặt Python packages"""
    print("📦 Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_python_version():
    """Kiểm tra Python version"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️  Warning: Python 3.8+ is recommended")
        return False
    return True

def start_server():
    """Khởi chạy ASCII Art server"""
    print("🚀 Starting ASCII Art Server...")
    try:
        # Run the server
        subprocess.run([sys.executable, "ascii_server.py"])
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

def main():
    print("🎨 ASCII Art Generator - Python Server Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        print("Please upgrade to Python 3.8+")
        return
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found!")
        return
    
    # Install dependencies
    if not install_requirements():
        print("Failed to install dependencies. Please check your Python environment.")
        return
    
    print("\n🎉 Setup completed successfully!")
    print("📡 Server will start on http://localhost:5000")
    print("🌐 Make sure your frontend is running on http://localhost:8000")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 50)
    
    # Wait a moment then start server
    time.sleep(2)
    start_server()

if __name__ == "__main__":
    main()
