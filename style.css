* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #fff;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
}

.upload-area:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #ffd700;
}

.upload-area.dragover {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-content p {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.upload-hint {
    font-size: 0.9rem;
    opacity: 0.8;
}

.controls-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: end;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 600;
    color: #555;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

.control-group input[type="range"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.control-group input[type="range"]:disabled::-webkit-slider-thumb {
    background: #ccc;
    cursor: not-allowed;
}

.control-group select {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
}

.control-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.control-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.width-hint {
    display: block;
    color: #666;
    font-size: 0.8rem;
    margin-top: 4px;
    font-style: italic;
}

.generate-btn, .debug-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.debug-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    padding: 10px 20px;
    font-size: 0.9rem;
}

.generate-btn:hover, .debug-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.result-section {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.image-preview h3,
.ascii-output h3 {
    margin-bottom: 15px;
    color: #555;
    font-size: 1.3rem;
}

.image-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.ascii-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.copy-btn, .download-btn, .twitch-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s ease;
}

.download-btn {
    background: #007bff;
}

.twitch-btn {
    background: #9146ff;
}

.copy-btn:hover {
    background: #218838;
}

.download-btn:hover {
    background: #0056b3;
}

.twitch-btn:hover {
    background: #772ce8;
}

.twitch-info {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 15px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.9rem;
}

.twitch-status.compatible {
    color: #28a745;
    font-weight: 600;
}

.twitch-status.incompatible {
    color: #dc3545;
    font-weight: 600;
}

.font-size-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.font-size-control input[type="range"] {
    width: 100px;
}

#asciiResult {
    background: #1a1a1a;
    color: #00ff00;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 6px;
    line-height: 0.8;
    overflow: auto;
    max-height: 600px;
    white-space: pre;
    border: 2px solid #333;
}

footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

@media (max-width: 768px) {
    .result-section {
        grid-template-columns: 1fr;
    }
    
    .controls {
        grid-template-columns: 1fr;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 40px 20px;
    }
}
