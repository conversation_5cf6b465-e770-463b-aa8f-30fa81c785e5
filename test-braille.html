<!DOCTYPE html>
<html>
<head>
    <title>Test Braille Algorithm</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { 
            font-family: 'Courier New', monospace; 
            font-size: 12px; 
            line-height: 1; 
            background: #1a1a1a; 
            color: #00ff00; 
            padding: 15px; 
            border-radius: 5px; 
            white-space: pre;
            margin: 10px 0;
        }
        .info { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🧪 Test Braille Algorithm</h1>
    
    <div class="info">
        <h3>✅ Braille Patterns đã được thêm thành công!</h3>
        <p><strong>Tính năng mới:</strong></p>
        <ul>
            <li>✅ Braille patterns được đặt làm mặc định</li>
            <li>✅ Thuật toán chuyển đổi 2x4 pixel matrix</li>
            <li>✅ Auto-adjust width khi chọn <PERSON>raille (60 ký tự)</li>
            <li>✅ Font size nhỏ hơn cho Braille (6px)</li>
            <li>✅ Debug mode để theo dõi quá trình chuyển đổi</li>
        </ul>
    </div>

    <div class="info">
        <h3>🎯 Cách test:</h3>
        <ol>
            <li>Mở <a href="http://localhost:8000" target="_blank">ASCII Art Generator</a></li>
            <li>Upload ảnh anime/cartoon nhỏ (như ảnh bạn gửi)</li>
            <li>Kiểm tra "Braille Patterns" đã được chọn</li>
            <li>Bật Debug Mode để xem thông tin chi tiết</li>
            <li>Tạo ASCII Art và so sánh với kết quả mong muốn</li>
        </ol>
    </div>

    <div class="info">
        <h3>🔧 Thuật toán Braille:</h3>
        <p>Mỗi ký tự Braille đại diện cho ma trận 2x4 pixel:</p>
        <pre>
Dot positions:    Binary mapping:
1 4               bit 0, bit 3
2 5               bit 1, bit 4  
3 6               bit 2, bit 5
7 8               bit 6, bit 7
        </pre>
        <p>Unicode range: U+2800 - U+28FF (Braille Patterns)</p>
    </div>

    <div class="info">
        <h3>📊 Ví dụ kết quả mong đợi:</h3>
        <div class="test-result">⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⡿⠋⠑⠈⠛⢿
⣿⣿⣿⣿⡿⠿⠿⠿⠿⠻⠻⠟⠻⠻⣿⣿⣿⣿⣿⣿⠏⠀⢀⢮⡱⡀⢸
⣿⣿⡟⠁⢀⡠⠀⠀⠤⣐⠆⡖⠤⠄⠈⠂⠩⠉⣿⠋⠀⢤⢋⢦⠓⣍⠦
⣿⣿⠁⠐⠃⠀⣀⡀⠒⠈⢚⡜⡀⠀⡰⢩⠖⡀⠀⠀⠸⠬⣍⠶⡙⢦⡹
⣿⠃⠀⣠⠋⢠⢓⠆⠘⣄⡀⠪⡱⠀⣇⢫⠜⣱⠀⠄⢨⢳⠸⣬⢙⢦⡱</div>
    </div>

    <div class="info">
        <h3>⚙️ Tham số khuyến nghị:</h3>
        <ul>
            <li><strong>Width:</strong> 40-80 ký tự (tự động đặt 60)</li>
            <li><strong>Contrast:</strong> 1.2-1.5 cho ảnh anime</li>
            <li><strong>Brightness:</strong> 1.0-1.2</li>
            <li><strong>Font size:</strong> 6-8px (tự động đặt 6px)</li>
        </ul>
    </div>

    <div class="info">
        <h3>🐛 Debug Info:</h3>
        <p>Khi bật Debug Mode, bạn sẽ thấy trong Console:</p>
        <ul>
            <li>Canvas size và image data info</li>
            <li>Braille conversion process</li>
            <li>Pixel errors (nếu có)</li>
            <li>Processing time</li>
        </ul>
    </div>

    <p><strong>🎉 Braille ASCII Art đã sẵn sàng! Hãy thử với ảnh anime của bạn!</strong></p>
</body>
</html>
