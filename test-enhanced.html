<!DOCTYPE html>
<html>
<head>
    <title>🧪 Test Enhanced ASCII Art Generator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh;
        }
        .test-card { 
            background: rgba(255,255,255,0.95); 
            color: #333; 
            padding: 25px; 
            border-radius: 15px; 
            margin: 20px 0; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .status { padding: 15px; border-radius: 8px; margin: 10px 0; }
        .success { background: #d4edda; border-left: 4px solid #28a745; }
        .warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .error { background: #f8d7da; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        h1 { text-align: center; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .btn { 
            background: #9146ff; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 8px; 
            display: inline-block; 
            margin: 10px 5px; 
            font-weight: 600;
            border: none;
            cursor: pointer;
        }
        .btn:hover { background: #772ce8; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: 'Courier New', monospace; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Enhanced ASCII Art Generator - Test Suite</h1>
    
    <div class="test-card">
        <h2>🔍 Server Status Check</h2>
        <div id="serverStatus" class="status info">
            <p>Đang kiểm tra server...</p>
        </div>
        <button onclick="checkServer()" class="btn">🔄 Refresh Server Status</button>
    </div>

    <div class="test-card">
        <h2>🎯 Enhanced Features Test</h2>
        <div class="info">
            <h4>✅ Features đã được cập nhật:</h4>
            <ul>
                <li><strong>Enhanced Processing Checkbox</strong>: ✨ Enhanced AI Processing</li>
                <li><strong>Dithering Method Selector</strong>: Floyd-Steinberg / Atkinson</li>
                <li><strong>Twitch Info Display</strong>: Compatibility status, char/line count</li>
                <li><strong>Copy for Twitch Button</strong>: 🎮 Copy cho Twitch</li>
                <li><strong>Enhanced Backend Endpoint</strong>: /convert_enhanced</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <h2>🚀 Test Instructions</h2>
        <div class="warning">
            <h4>Cách test Enhanced Generator:</h4>
            <ol>
                <li><strong>Mở ứng dụng</strong>: <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></li>
                <li><strong>Upload ảnh</strong>: Sử dụng lopa-4x.png hoặc ảnh anime khác</li>
                <li><strong>Kiểm tra settings</strong>:
                    <ul>
                        <li>✅ "✨ Enhanced AI Processing" được tích</li>
                        <li>Dithering Method: Floyd-Steinberg</li>
                        <li>Contrast: 1.3-1.5</li>
                        <li>Brightness: 1.1-1.2</li>
                        <li>Width: 38 (cố định)</li>
                    </ul>
                </li>
                <li><strong>Tạo ASCII Art</strong>: Click "🎨 Tạo ASCII Art"</li>
                <li><strong>Kiểm tra kết quả</strong>:
                    <ul>
                        <li>ASCII art có chi tiết cao</li>
                        <li>Twitch compatibility info hiển thị</li>
                        <li>Nút "🎮 Copy cho Twitch" hoạt động</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <div class="test-card">
        <h2>🎯 Expected Results</h2>
        <div class="success">
            <h4>Kết quả mong đợi với Enhanced Processing:</h4>
            <div class="code">
⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⡿⠋⠑⠈⠛⢿
⣿⣿⣿⣿⡿⠿⠿⠿⠿⠻⠻⠟⠻⠻⣿⣿⣿⣿⣿⣿⠏⠀⢀⢮⡱⡀⢸
⣿⣿⡟⠁⢀⡠⠀⠀⠤⣐⠆⡖⠤⠄⠈⠂⠩⠉⣿⠋⠀⢤⢋⢦⠓⣍⠦
⣿⣿⠁⠐⠃⠀⣀⡀⠒⠈⢚⡜⡀⠀⡰⢩⠖⡀⠀⠀⠸⠬⣍⠶⡙⢦⡹
⣿⠃⠀⣠⠋⢠⢓⠆⠘⣄⡀⠪⡱⠀⣇⢫⠜⣱⠀⠄⢨⢳⠸⣬⢙⢦⡱
            </div>
            <p><small>Chi tiết cao, gradient mượt, giống ảnh gốc như assets.txt!</small></p>
        </div>
        
        <div class="info">
            <h4>Twitch Compatibility Info:</h4>
            <ul>
                <li><strong>✅ Twitch Compatible</strong> - Sẵn sàng paste vào chat</li>
                <li><strong>Character count</strong>: 456/500 chars</li>
                <li><strong>Line count</strong>: 12/15 lines</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <h2>🐛 Troubleshooting</h2>
        <div class="error">
            <h4>Nếu gặp lỗi "Không thể kết nối Enhanced Python server":</h4>
            <ol>
                <li>Kiểm tra enhanced server có đang chạy không</li>
                <li>Restart server: <code>python enhanced_ascii_server.py</code></li>
                <li>Kiểm tra port 5000 có bị chiếm không</li>
                <li>Xem console (F12) để debug chi tiết</li>
            </ol>
        </div>
        
        <div class="warning">
            <h4>Nếu chất lượng chưa như mong muốn:</h4>
            <ul>
                <li>Thử tăng Contrast lên 1.4-1.5</li>
                <li>Sử dụng Atkinson dithering cho ảnh high contrast</li>
                <li>Đảm bảo Enhanced Processing đã được bật</li>
                <li>Test với ảnh có độ tương phản cao</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <h2>🚀 Quick Actions</h2>
        <div style="text-align: center;">
            <a href="http://localhost:8000" target="_blank" class="btn">🎨 Open Enhanced Generator</a>
            <button onclick="testAPI()" class="btn">🧪 Test API Endpoint</button>
            <a href="http://localhost:5000/health" target="_blank" class="btn">🔍 Check Server Health</a>
        </div>
        <div id="apiTestResult" class="test-result"></div>
    </div>

    <script>
        async function checkServer() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.innerHTML = '<p>Đang kiểm tra server...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                
                if (data.status === 'healthy' && data.twitch_optimized) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        <h4>✅ Enhanced Server đang hoạt động tốt!</h4>
                        <p><strong>Status:</strong> ${data.status}</p>
                        <p><strong>Message:</strong> ${data.message}</p>
                        <p><strong>Twitch Optimized:</strong> ${data.twitch_optimized ? 'Yes' : 'No'}</p>
                    `;
                } else {
                    statusDiv.className = 'status warning';
                    statusDiv.innerHTML = '<h4>⚠️ Server phản hồi nhưng có vấn đề</h4>';
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <h4>❌ Không thể kết nối Enhanced Server</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Hãy đảm bảo enhanced server đang chạy trên port 5000</p>
                `;
            }
        }
        
        async function testAPI() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '<p>Đang test API endpoint...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/convert_enhanced', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        image: 'data:image/png;base64,test',
                        width: 38,
                        contrast: 1.3,
                        brightness: 1.1,
                        dithering_method: 'floyd_steinberg'
                    })
                });
                
                if (response.status === 400) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ API endpoint /convert_enhanced hoạt động! (Error 400 là bình thường với test data)';
                } else {
                    resultDiv.className = 'test-result warning';
                    resultDiv.innerHTML = `⚠️ API phản hồi với status: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Không thể test API: ${error.message}`;
            }
        }
        
        // Auto check server on load
        window.onload = checkServer;
    </script>
</body>
</html>
