<!DOCTYPE html>
<html>
<head>
    <title>Test ASCII Art Improvements</title>
</head>
<body>
    <h2><PERSON><PERSON><PERSON> cải tiến đã thực hiện:</h2>
    <ul>
        <li>✅ <strong>C<PERSON>i thiện thuật toán xử lý pixel</strong>: <PERSON><PERSON><PERSON> tra bounds tốt hơn, tránh bỏ sót pixel ở cạnh phải</li>
        <li>✅ <strong>Thêm Debug Mode</strong>: Hiển thị thông tin chi tiết về quá trình chuyển đổi</li>
        <li>✅ <strong>Cải thiện chất lượng <PERSON>vas</strong>: Sử dụng imageSmoothingEnabled và imageSmoothingQuality</li>
        <li>✅ <strong>Xử lý lỗi tốt hơn</strong>: Fallback cho pixel không hợp lệ</li>
        <li>✅ <strong>Tối ưu thuật toán mapping</strong>: <PERSON><PERSON><PERSON> to<PERSON> chính xác hơn cho block character</li>
    </ul>
    
    <h2>Cách test:</h2>
    <ol>
        <li>Mở <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></li>
        <li>Upload một ảnh có background rõ ràng</li>
        <li>Click nút "🔍 Debug Mode" để bật debug</li>
        <li>Tạo ASCII Art và kiểm tra console (F12) để xem thông tin debug</li>
        <li>Kiểm tra xem cạnh phải của ASCII art có đầy đủ không</li>
    </ol>
    
    <h2>Các tham số được recommend:</h2>
    <ul>
        <li><strong>Ảnh có background rõ ràng</strong>: Density Blocks, Width 80-120</li>
        <li><strong>Ảnh đơn giản</strong>: Shade Blocks, Width 40-60</li>
        <li><strong>Ảnh tối</strong>: Tăng Brightness lên 1.2-1.5</li>
        <li><strong>Ảnh nhạt</strong>: Tăng Contrast lên 1.3-1.8</li>
    </ul>
</body>
</html>
